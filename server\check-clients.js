import { db } from './src/db/database.js';

async function checkClients() {
  try {
    const clients = await db
      .selectFrom('oauth_clients')
      .selectAll()
      .execute();

    console.log('OAuth Clients:');
    clients.forEach(client => {
      console.log(`\nClient: ${client.client_name}`);
      console.log(`Client ID: ${client.client_id}`);
      console.log(`Client Secret: ${client.client_secret}`);
      console.log(`Redirect URIs: ${client.redirect_uris}`);
      console.log(`Grant Types: ${client.grant_types}`);
      console.log(`Scope: ${client.scope}`);
      console.log(`Active: ${client.is_active}`);
    });
  } catch (error) {
    console.error('Error:', error);
  } finally {
    process.exit(0);
  }
}

checkClients();
