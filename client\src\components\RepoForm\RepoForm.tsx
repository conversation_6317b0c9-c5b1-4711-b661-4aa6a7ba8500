import React from 'react'
import { useNavigate } from 'react-router-dom'
import { useForm, Controller, type SubmitHandler } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useMo } from '@hooks/useMos'
import { useCreateRepo } from '@hooks/useRepos'
import { useActiveWorkAreas } from '@hooks/useMaterials'
import { Button } from '@components/Button/Button'
import { Input, Textarea, Select } from '@components/Input/Input'
import { Label } from '@components/Label/Label'
import { FormGroup } from '@components/FormGroup/FormGroup'
import { FormRow, FormActions } from '@components/Form/Form'
import { Checkbox, CheckboxGroup } from '@components/Checkbox/Checkbox'
import { repoFormSchema, type RepoFormData } from '../../schemas/repoFormSchema'

interface RepoFormProps {
  moId: number
  onBack: () => void
}

export const RepoForm: React.FC<RepoFormProps> = ({ moId, onBack }) => {
  const navigate = useNavigate()
  const { data: mo, isLoading: isLoadingMo } = useMo(moId)
  const { data: workAreas } = useActiveWorkAreas()
  const createRepoMutation = useCreateRepo()

  const {
    control,
    handleSubmit,
    formState: { errors },
    watch,
    setValue
  } = useForm<RepoFormData>({
    resolver: zodResolver(repoFormSchema),
    defaultValues: {
      creator_email: '',
      repo_type: '',
      customer_fault: false,
      units_affected: 0,
      items: '',
      materials: '',
      printer: '',
      rework_reason: '',
      notes: '',
      work_areas: []
    }
  })

  const watchedWorkAreas = watch('work_areas')

  const handleWorkAreaChange = (areaId: number, checked: boolean) => {
    const currentAreas = watchedWorkAreas || []
    const newAreas = checked
      ? [...currentAreas, areaId]
      : currentAreas.filter(id => id !== areaId)
    setValue('work_areas', newAreas)
  }

  const onSubmit: SubmitHandler<RepoFormData> = async (data) => {
    try {
      // Add the mo_id to the form data
      const formDataWithMoId = {
        ...data,
        mo_id: moId,
        selected_materials: [] // Add empty array for now
      }
      await createRepoMutation.mutateAsync(formDataWithMoId)
      navigate('/active-repos')
    } catch (error) {
      console.error('Error creating repo:', error)
    }
  }

  if (isLoadingMo) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mb-4"></div>
        <p className="text-gray-600">Loading manufacturing order details...</p>
      </div>
    )
  }

  if (!mo) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Manufacturing Order Not Found</h3>
        <p className="text-gray-600 mb-4">The selected manufacturing order could not be found.</p>
        <Button onClick={onBack} variant="secondary">
          Back to Selection
        </Button>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <div className="card">
        <div className="flex justify-between items-start mb-4">
          <h2 className="text-xl font-semibold text-gray-900">Selected Manufacturing Order</h2>
          <Button onClick={onBack} variant="secondary" size="sm">
            Change Selection
          </Button>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg">
          <div>
            <span className="text-sm font-medium text-gray-500">MO ID</span>
            <p className="text-sm text-gray-900">{mo.mo_id}</p>
          </div>
          <div>
            <span className="text-sm font-medium text-gray-500">Order</span>
            <p className="text-sm text-gray-900">{mo.mo_order}</p>
          </div>
          <div>
            <span className="text-sm font-medium text-gray-500">Customer</span>
            <p className="text-sm text-gray-900">{mo.customer}</p>
          </div>
          <div>
            <span className="text-sm font-medium text-gray-500">Number</span>
            <p className="text-sm text-gray-900">{mo.num}</p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="card">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Create Reposition</h2>

        <FormRow columns={2} className="mb-6">
          <FormGroup>
            <Label htmlFor="creator_email" required>Creator Email</Label>
            <Controller
              name="creator_email"
              control={control}
              render={({ field, fieldState }) => (
                <Input
                  type="email"
                  id="creator_email"
                  placeholder="Enter creator email"
                  error={fieldState.error?.message}
                  {...field}
                />
              )}
            />
          </FormGroup>

          <FormGroup>
            <Label htmlFor="repo_type" required>Reposition Type</Label>
            <Controller
              name="repo_type"
              control={control}
              render={({ field, fieldState }) => (
                <Select
                  id="repo_type"
                  error={fieldState.error?.message}
                  {...field}
                >
                  <option value="">Select type</option>
                  <option value="Corte">Corte</option>
                  <option value="Corte Sub">Corte Sub</option>
                  <option value="Full Process">Full Process</option>
                  <option value="Materials Only">Materials Only</option>
                </Select>
              )}
            />
          </FormGroup>
        </FormRow>

        <FormRow columns={2} className="mb-6">
          <Label htmlFor="repo_type" required>Customer Fault</Label>
          <Controller
            name="customer_fault"
            control={control}
            render={({ field }) => (
              <Checkbox
                id="customer_fault"
                label="Yes"
                checked={field.value}
                onChange={field.onChange}
              />
            )}
          />

          <FormGroup>
            <Label htmlFor="units_affected">Units Affected</Label>
            <Controller
              name="units_affected"
              control={control}
              render={({ field, fieldState }) => (
                <Input
                  type="number"
                  id="units_affected"
                  min="0"
                  error={fieldState.error?.message}
                  {...field}
                  onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                />
              )}
            />
          </FormGroup>
        </FormRow>

        <FormRow columns={2} className="mb-6">
          <FormGroup>
            <Label htmlFor="items">Items</Label>
            <Controller
              name="items"
              control={control}
              render={({ field, fieldState }) => (
                <Textarea
                  id="items"
                  placeholder="Enter items"
                  rows={4}
                  error={fieldState.error?.message}
                  {...field}
                />
              )}
            />
          </FormGroup>

          <FormGroup>
            <Label htmlFor="materials">Materials</Label>
            <Controller
              name="materials"
              control={control}
              render={({ field, fieldState }) => (
                <Textarea
                  id="materials"
                  placeholder="Enter materials"
                  rows={4}
                  error={fieldState.error?.message}
                  {...field}
                />
              )}
            />
          </FormGroup>
        </FormRow>

        <FormRow columns={2} className="mb-6">
          <FormGroup>
            <Label htmlFor="printer">Printer</Label>
            <Controller
              name="printer"
              control={control}
              render={({ field, fieldState }) => (
                <Input
                  type="text"
                  id="printer"
                  placeholder="Enter printer"
                  error={fieldState.error?.message}
                  {...field}
                />
              )}
            />
          </FormGroup>

          <FormGroup>
            <Label htmlFor="rework_reason">Rework Reason</Label>
            <Controller
              name="rework_reason"
              control={control}
              render={({ field, fieldState }) => (
                <Input
                  type="text"
                  id="rework_reason"
                  placeholder="Enter rework reason"
                  error={fieldState.error?.message}
                  {...field}
                />
              )}
            />
          </FormGroup>
        </FormRow>

        <FormGroup className="mb-6">
          <Label htmlFor="notes">Notes</Label>
          <Controller
            name="notes"
            control={control}
            render={({ field, fieldState }) => (
              <Textarea
                id="notes"
                placeholder="Enter notes"
                rows={3}
                error={fieldState.error?.message}
                {...field}
              />
            )}
          />
        </FormGroup>

        {workAreas && workAreas.length > 0 && (
          <CheckboxGroup label="Work Areas" className="mb-6">
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
              {workAreas.map((area) => (
                <Checkbox
                  key={area.id}
                  id={`work_area_${area.id}`}
                  label={area.name}
                  checked={(watchedWorkAreas || []).includes(area.id)}
                  onChange={(e) => handleWorkAreaChange(area.id, e.target.checked)}
                />
              ))}
            </div>
          </CheckboxGroup>
        )}

        <FormActions>
          <Button type="button" onClick={onBack} variant="secondary">
            Cancel
          </Button>
          <Button
            type="submit"
            variant="primary"
            loading={createRepoMutation.isPending}
            disabled={createRepoMutation.isPending}
          >
            {createRepoMutation.isPending ? 'Creating...' : 'Create Reposition'}
          </Button>
        </FormActions>
      </form>
    </div>
  )
}


