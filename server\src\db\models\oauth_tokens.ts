import { ColumnType, Generated } from 'kysely'

export interface OAuthTokensTable {
  id: Generated<number>
  access_token: string
  refresh_token: string | null
  token_type: string // 'Bearer'
  expires_at: ColumnType<Date, string | undefined, string | undefined>
  scope: string | null
  client_id: string
  user_id: number | null // For password grant type
  authorization_code: string | null // For authorization code flow
  code_challenge: string | null // For PKCE
  code_challenge_method: string | null // 'S256' or 'plain'
  redirect_uri: string | null
  is_revoked: boolean
  created_at: ColumnType<Date, string | undefined, never>
  updated_at: ColumnType<Date, string | undefined, string | undefined>
}

export type OAuthToken = {
  id: number
  access_token: string
  refresh_token: string | null
  token_type: string
  expires_at: Date
  scope: string | null
  client_id: string
  user_id: number | null
  authorization_code: string | null
  code_challenge: string | null
  code_challenge_method: string | null
  redirect_uri: string | null
  is_revoked: boolean
  created_at: Date
  updated_at: Date
}

export type NewOAuthToken = {
  access_token: string
  refresh_token?: string | null
  token_type: string
  expires_at: string | Date
  scope?: string | null
  client_id: string
  user_id?: number | null
  authorization_code?: string | null
  code_challenge?: string | null
  code_challenge_method?: string | null
  redirect_uri?: string | null
  is_revoked?: boolean
}

export type OAuthTokenUpdate = {
  is_revoked?: boolean
  refresh_token?: string | null
}
