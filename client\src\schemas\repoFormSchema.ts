import { z } from 'zod'

export const repoFormSchema = z.object({
  creator_email: z
    .string()
    .min(1, 'Creator email is required')
    .email('Please enter a valid email address'),

  repo_type: z.string().min(1, 'Reposition type is required'),

  customer_fault: z.boolean(),

  units_affected: z.number().min(0, 'Units affected must be 0 or greater'),

  items: z.string().optional().default(''),

  materials: z.string().optional().default(''),

  printer: z.string().optional().default(''),

  rework_reason: z.string().optional().default(''),

  notes: z.string().optional().default(''),

  work_areas: z.array(z.number()).default([]),
})

export type RepoFormData = z.infer<typeof repoFormSchema>
