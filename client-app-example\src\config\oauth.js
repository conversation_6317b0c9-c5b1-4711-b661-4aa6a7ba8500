// OAuth 2.0 Configuration
export const OAUTH_CONFIG = {
  // OAuth Server URLs
  authServerUrl: 'http://localhost:3230',

  // OAuth Client Configuration (Web Application - supports authorization_code flow)
  clientId: '9e842606-0fb6-4a5d-8557-87f545add7bf', // Web client from seed data
  clientSecret: 'daa85a68ad8220b86e3f133ec8a800756a21f6c3b20fde0cabe8113cb485cc5d',

  // Mobile client for password flow (if needed)
  mobileClientId: '07b2f434-ec60-4cac-aa53-f17008ab5e6d',
  mobileClientSecret: '6eac4814cddc514c84cffaf691aae3b3d7dd83b12c56a3ea93f3aa226c0576fa',

  // OAuth Endpoints
  endpoints: {
    authorize: '/oauth/authorize',
    token: '/oauth/token',
    revoke: '/oauth/revoke',
    userInfo: '/api/v1/users/me',
    jwks: '/.well-known/jwks.json',
    metadata: '/.well-known/oauth-authorization-server'
  },

  // OAuth Flow Configuration
  redirectUri: 'http://localhost:3000/callback',
  scope: 'read write',

  // Grant Types
  grantTypes: {
    authorizationCode: 'authorization_code',
    password: 'password',
    refreshToken: 'refresh_token'
  },

  // Token Storage Keys
  storage: {
    accessToken: 'oauth_access_token',
    refreshToken: 'oauth_refresh_token',
    tokenExpiry: 'oauth_token_expiry',
    userInfo: 'oauth_user_info'
  }
}

// Helper function to get full URL
export const getOAuthUrl = (endpoint) => {
  return `${OAUTH_CONFIG.authServerUrl}${endpoint}`
}

// Helper function to build authorization URL
export const buildAuthorizationUrl = (state = null) => {
  const params = new URLSearchParams({
    response_type: 'code',
    client_id: OAUTH_CONFIG.clientId,
    redirect_uri: OAUTH_CONFIG.redirectUri,
    scope: OAUTH_CONFIG.scope,
    ...(state && { state })
  })

  return `${getOAuthUrl(OAUTH_CONFIG.endpoints.authorize)}?${params.toString()}`
}
