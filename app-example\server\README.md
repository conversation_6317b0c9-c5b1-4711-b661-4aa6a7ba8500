# App Example Server

A simple Node.js/Express server demonstrating OAuth 2.0 JWT verification with employee information middleware.

## 🎯 Purpose

This server demonstrates how backend services can:
- Verify RS256 JWT tokens from the OAuth authorization server
- Fetch employee information using OAuth tokens
- Implement authentication middleware for protected endpoints
- Integrate with the OAuth server's <PERSON><PERSON><PERSON> endpoint for public key discovery

## ✨ Features

### 🔐 Authentication Middleware
- **JWT Verification** - RS256 signature validation using JWKS
- **Employee Information** - Automatic employee data fetching
- **Token Caching** - JWKS caching for performance
- **Error Handling** - Comprehensive error responses

### 🛡️ Security Features
- **CORS Protection** - Configured for client app origin
- **Helmet Security** - Security headers and protection
- **Token Validation** - Cryptographic signature verification
- **Request Logging** - Authentication attempt logging

### 📡 API Endpoints
- **Public Endpoints** - Health check and server info
- **Protected Endpoints** - Employee profile, dashboard, tasks
- **Optional Auth** - Endpoints that work with or without tokens
- **Test Endpoints** - Middleware testing and verification

## 🚀 Quick Start

### Prerequisites
- OAuth authorization server running on `http://localhost:3230`
- Node.js 18+ installed

### Installation
```bash
cd client-app-example/server
npm install
```

### Configuration
Create or update `.env` file:
```env
PORT=3001
OAUTH_SERVER_URL=http://localhost:3230
JWKS_URL=http://localhost:3230/.well-known/jwks.json
CORS_ORIGIN=http://localhost:3000
JWT_ALGORITHM=RS256
JWT_ISSUER=oauth2-auth-server
```

### Start Server
```bash
# Development mode with auto-reload
npm run dev

# Production mode
npm start
```

Server runs on: `http://localhost:3001`

### Test Server
```bash
npm test
```

## 📋 API Endpoints

### Public Endpoints

#### `GET /`
Server information and available endpoints
```json
{
  "message": "Client App Example Server",
  "version": "1.0.0",
  "endpoints": { ... },
  "features": [ ... ]
}
```

#### `GET /api/health`
Health check endpoint
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "server": "client-app-example-server"
}
```

#### `GET /api/info`
Server info with optional authentication
- **No token**: Basic server information
- **With token**: Includes user and employee data

### Protected Endpoints

#### `GET /api/profile`
**Authentication Required**
```bash
curl -H "Authorization: Bearer <token>" http://localhost:3001/api/profile
```

Returns complete employee profile with token information.

#### `GET /api/dashboard`
**Authentication Required**
```bash
curl -H "Authorization: Bearer <token>" http://localhost:3001/api/dashboard
```

Returns dashboard data including:
- Welcome message
- Employee information
- Permissions and scope
- Server information

#### `GET /api/tasks`
**Authentication Required**
```bash
curl -H "Authorization: Bearer <token>" http://localhost:3001/api/tasks
```

Returns employee tasks and activities (mock data).

#### `GET /api/verify-token`
**Authentication Required**
```bash
curl -H "Authorization: Bearer <token>" http://localhost:3001/api/verify-token
```

Returns token verification details and payload.

### Test Endpoints

#### `GET /test-auth`
**Authentication Required**

Tests the complete middleware chain:
1. JWT token verification
2. Employee information fetching

## 🔧 Middleware Usage

### Authentication Middleware

```javascript
import authMiddleware from './middleware/auth.js'

// Require valid JWT token
app.get('/protected', authMiddleware.authenticate(), (req, res) => {
  // req.tokenPayload contains JWT claims
  // req.accessToken contains the raw token
  res.json({ user: req.tokenPayload })
})

// Get employee information
app.get('/profile',
  authMiddleware.authenticate(),
  authMiddleware.getEmployee(),
  (req, res) => {
    // req.employee contains employee data from OAuth server
    res.json({ employee: req.employee })
  }
)

// Optional authentication
app.get('/info', authMiddleware.optionalAuth(), (req, res) => {
  // Works with or without token
  const authenticated = !!req.tokenPayload
  res.json({ authenticated, user: req.tokenPayload })
})
```

### Middleware Methods

#### `authenticate()`
- Verifies JWT token signature using RS256
- Validates token claims (issuer, expiry, etc.)
- Adds `tokenPayload` and `accessToken` to request
- Returns 401 if token is invalid

#### `getEmployee()`
- Fetches employee information from OAuth server
- Requires `authenticate()` middleware to run first
- Adds `employee` object to request
- Returns 403 if employee data cannot be retrieved

#### `optionalAuth()`
- Attempts authentication but doesn't fail if no token
- Useful for endpoints that enhance functionality with auth
- Adds `tokenPayload`, `accessToken`, and `employee` if available

## 🔍 How It Works

### 1. Token Verification Process
```
1. Extract Bearer token from Authorization header
2. Fetch JWKS from OAuth server (with caching)
3. Verify token signature using RS256 public key
4. Validate token claims (issuer, expiry, audience)
5. Add token payload to request object
```

### 2. Employee Information Fetching
```
1. Use verified access token
2. Call OAuth server's /api/v1/users/me endpoint
3. Retrieve complete employee profile
4. Add employee data to request object
```

### 3. JWKS Caching
```
1. Fetch JWKS from OAuth server
2. Cache for 1 hour (configurable)
3. Use cached keys for token verification
4. Refresh cache when expired or on error
```

## 🧪 Testing

### Manual Testing

1. **Get OAuth Token**:
```bash
curl -X POST http://localhost:3230/oauth/token \
  -H "Content-Type: application/json" \
  -d '{
    "grant_type": "password",
    "username": "william de jesus.rivera marroquin",
    "password": "admin123",
    "client_id": "07b2f434-ec60-4cac-aa53-f17008ab5e6d",
    "client_secret": "6eac4814cddc514c84cffaf691aae3b3d7dd83b12c56a3ea93f3aa226c0576fa"
  }'
```

2. **Test Protected Endpoint**:
```bash
curl -H "Authorization: Bearer <access_token>" \
  http://localhost:3001/api/profile
```

### Automated Testing
```bash
npm test
```

The test suite verifies:
- ✅ Server health and info endpoints
- ✅ OAuth token acquisition
- ✅ JWT token verification
- ✅ Employee middleware functionality
- ✅ Protected endpoint access
- ✅ Error handling for invalid tokens

## 🔒 Security Considerations

### Token Security
- Tokens are verified cryptographically using RS256
- Public keys are fetched from trusted JWKS endpoint
- Token expiry is validated
- Invalid tokens are rejected with appropriate errors

### CORS Configuration
- Configured to allow requests from client app origin
- Credentials support for authenticated requests
- Proper headers for cross-origin requests

### Error Handling
- Sensitive information is not exposed in error messages
- Authentication failures are logged
- Graceful degradation for optional authentication

## 🚀 Production Deployment

### Environment Variables
```env
NODE_ENV=production
PORT=3001
OAUTH_SERVER_URL=https://oauth.yourdomain.com
JWKS_URL=https://oauth.yourdomain.com/.well-known/jwks.json
CORS_ORIGIN=https://app.yourdomain.com
JWKS_CACHE_TTL=3600000
```

### Security Enhancements
- Use HTTPS in production
- Implement rate limiting
- Add request validation
- Set up monitoring and logging
- Configure proper CORS origins

## 📚 Integration Examples

This server demonstrates patterns for:
- **Microservices** - Service-to-service authentication
- **API Gateways** - Token verification middleware
- **Backend Services** - Employee data integration
- **Mobile Apps** - JWT token validation
- **Third-party APIs** - OAuth token verification

---

**🎉 Ready for Production Use!**

This example server provides production-ready patterns for OAuth 2.0 JWT verification and employee information middleware.
