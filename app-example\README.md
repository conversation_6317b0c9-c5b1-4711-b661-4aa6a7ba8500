# OAuth 2.0 App Example

A comprehensive React-based example application demonstrating OAuth 2.0 integration with RS256 JWT tokens.

## 🎯 Purpose

This example application demonstrates how to integrate with an OAuth 2.0 authorization server that uses RS256 (RSA with SHA-256) for JWT token signing. It showcases both **Password Grant** and **Authorization Code Grant** flows with proper token verification and management.

## ✨ Features Demonstrated

### 🔐 OAuth 2.0 Flows
- **Password Grant Flow** - Direct username/password authentication
- **Authorization Code Flow** - Redirect-based authentication with PKCE
- **Refresh Token Flow** - Automatic token renewal

### 🔑 RS256 JWT Integration
- **Token Verification** - Using public key from JWKS endpoint
- **Token Decoding** - Displaying JWT claims and metadata
- **Signature Validation** - Real-time verification of token integrity
- **Public Key Discovery** - Automatic JWKS fetching and caching

### 🛡️ Security Features
- **Automatic Token Refresh** - Seamless re-authentication
- **Secure Token Storage** - Browser localStorage with expiry handling
- **CSRF Protection** - State parameter validation
- **Protected Routes** - Route-level authentication guards
- **API Interceptors** - Automatic token injection and error handling

### 📱 User Experience
- **Responsive Design** - Works on desktop and mobile
- **Real-time Updates** - Live token status and verification
- **Error Handling** - Comprehensive error messages and recovery
- **Loading States** - User-friendly loading indicators

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ installed
- OAuth server running on `http://localhost:3230`
- Valid OAuth client credentials

### Installation

1. **Install dependencies:**
   ```bash
   cd client-app-example
   npm install
   ```

2. **Configure OAuth settings:**
   Edit `src/config/oauth.js` with your OAuth server details:
   ```javascript
   export const OAUTH_CONFIG = {
     authServerUrl: 'http://localhost:3230',
     clientId: 'your-client-id',
     clientSecret: 'your-client-secret',
     redirectUri: 'http://localhost:3000/callback',
     scope: 'read write'
   }
   ```

3. **Start the development server:**
   ```bash
   npm run dev
   ```

4. **Open your browser:**
   Navigate to `http://localhost:3000`

## 🔧 Configuration

### OAuth Client Setup

The application expects an OAuth client configured with:
- **Grant Types:** `password`, `authorization_code`, `refresh_token`
- **Redirect URI:** `http://localhost:3000/callback`
- **Scope:** `read write`

### Environment Variables

Create a `.env` file (optional):
```env
VITE_OAUTH_SERVER_URL=http://localhost:3230
VITE_CLIENT_ID=your-client-id
VITE_CLIENT_SECRET=your-client-secret
```

## 📖 Usage Guide

### 1. Login Methods

#### Password Flow
- Enter username and password directly
- Immediate authentication without redirects
- Best for trusted applications

#### Authorization Code Flow
- Redirects to OAuth server login page
- More secure for public clients
- Supports PKCE for enhanced security

### 2. Dashboard Features

- **User Information** - Display authenticated user details
- **Token Analysis** - Real-time JWT token inspection
- **Server Metadata** - OAuth server configuration
- **Verification Status** - RS256 signature validation

### 3. Token Management

- **Automatic Refresh** - Tokens renewed before expiry
- **Manual Refresh** - Force token renewal
- **Token Inspection** - Decode and verify JWT claims
- **JWKS Integration** - Public key discovery and caching

### 4. API Testing

- **Protected Endpoints** - Test OAuth-secured APIs
- **Error Handling** - Graceful handling of auth failures
- **Request Inspection** - View API request/response details

## 🏗️ Architecture

### Component Structure
```
src/
├── components/          # Reusable UI components
│   ├── Navbar.jsx      # Navigation with auth state
│   └── LoadingSpinner.jsx
├── contexts/           # React contexts
│   └── AuthContext.jsx # Authentication state management
├── pages/              # Route components
│   ├── LoginPage.jsx   # Authentication forms
│   ├── DashboardPage.jsx # Main dashboard
│   ├── TokenPage.jsx   # Token analysis
│   ├── ProfilePage.jsx # User profile
│   └── CallbackPage.jsx # OAuth callback handler
├── services/           # Business logic
│   └── oauthService.js # OAuth integration
└── config/             # Configuration
    └── oauth.js        # OAuth settings
```

### Key Services

#### OAuthService
- Token management and storage
- API communication with OAuth server
- JWT verification using JWKS
- Automatic token refresh logic

#### AuthContext
- Global authentication state
- User session management
- Error handling and loading states
- Route protection logic

## 🔍 Token Verification Process

1. **Token Reception** - Receive JWT from OAuth server
2. **JWKS Fetching** - Download public key from `/.well-known/jwks.json`
3. **Signature Verification** - Validate token using RS256 public key
4. **Claims Validation** - Check issuer, audience, expiry
5. **Token Storage** - Securely store verified token

## 🛠️ Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

### Testing OAuth Flows

1. **Start OAuth Server** - Ensure server is running on port 3230
2. **Seed Test Data** - Run server seed script for test users
3. **Test Credentials:**
   - Username: `william de jesus.rivera marroquin`
   - Password: `admin123`

### Debugging

- Check browser console for detailed logs
- Inspect Network tab for API requests
- Use Token page for JWT analysis
- Verify JWKS endpoint accessibility

## 🔒 Security Considerations

### Token Storage
- Tokens stored in localStorage (consider httpOnly cookies for production)
- Automatic cleanup on logout
- Expiry-based validation

### CSRF Protection
- State parameter validation in authorization code flow
- Secure redirect URI validation

### Token Verification
- Always verify JWT signatures using public key
- Validate all standard JWT claims (iss, aud, exp)
- Handle verification errors gracefully

## 📚 Learning Resources

### OAuth 2.0 Concepts
- **Grant Types** - Different authentication flows
- **Scopes** - Permission-based access control
- **Tokens** - Access tokens vs refresh tokens
- **PKCE** - Proof Key for Code Exchange

### JWT & RS256
- **JWT Structure** - Header, payload, signature
- **RS256 Algorithm** - RSA with SHA-256
- **JWKS** - JSON Web Key Set for public key distribution
- **Token Verification** - Signature validation process

## 🤝 Contributing

This is an example application for demonstration purposes. Feel free to:
- Fork and modify for your use case
- Report issues or suggest improvements
- Use as a reference for your OAuth integration

## 📄 License

MIT License - Feel free to use this code in your projects.

---

**Happy OAuth Integration!** 🎉

This example demonstrates production-ready OAuth 2.0 integration patterns with modern security practices.
