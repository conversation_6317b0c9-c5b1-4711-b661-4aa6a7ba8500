{"name": "app-example-server", "version": "1.0.0", "description": "Express server for OAuth app example with HTTP-only cookies and employee information middleware", "type": "module", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "node test-server.js"}, "dependencies": {"axios": "^1.6.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "jose": "^5.1.3", "jsonwebtoken": "^9.0.2"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["oauth2", "jwt", "rs256", "express", "middleware", "employee"], "author": "OAuth Example", "license": "MIT"}