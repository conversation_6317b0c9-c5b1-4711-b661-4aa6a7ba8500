import axios from 'axios'
import { jwtDecode } from 'jwt-decode'
import * as jose from 'jose'
import { OAUTH_CONFIG, getOAuthUrl, buildAuthorizationUrl } from '../config/oauth.js'

class OAuthService {
  constructor() {
    this.publicKey = null
    this.jwks = null
    this.setupAxiosInterceptors()
  }

  // Setup axios interceptors for automatic token handling
  setupAxiosInterceptors() {
    // Request interceptor to add auth header
    axios.interceptors.request.use(
      (config) => {
        const token = this.getAccessToken()
        if (token && !config.headers.Authorization) {
          config.headers.Authorization = `Bearer ${token}`
        }
        return config
      },
      (error) => Promise.reject(error)
    )

    // Response interceptor to handle token expiry
    axios.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response?.status === 401 && this.getAccessToken()) {
          // Token might be expired, try to refresh
          const refreshed = await this.refreshToken()
          if (refreshed) {
            // Retry the original request
            return axios.request(error.config)
          } else {
            // Refresh failed, redirect to login
            this.logout()
          }
        }
        return Promise.reject(error)
      }
    )
  }

  // Get OAuth server metadata
  async getServerMetadata() {
    try {
      const response = await axios.get(getOAuthUrl(OAUTH_CONFIG.endpoints.metadata))
      return response.data
    } catch (error) {
      console.error('Failed to get server metadata:', error)
      throw error
    }
  }

  // Get JWKS for token verification
  async getJWKS() {
    if (this.jwks) return this.jwks

    try {
      const response = await axios.get(getOAuthUrl(OAUTH_CONFIG.endpoints.jwks))
      this.jwks = response.data
      return this.jwks
    } catch (error) {
      console.error('Failed to get JWKS:', error)
      throw error
    }
  }

  // Password grant flow
  async loginWithPassword(username, password) {
    try {
      const response = await axios.post(getOAuthUrl(OAUTH_CONFIG.endpoints.token), {
        grant_type: OAUTH_CONFIG.grantTypes.password,
        username,
        password,
        client_id: OAUTH_CONFIG.mobileClientId,
        client_secret: OAUTH_CONFIG.mobileClientSecret,
        scope: OAUTH_CONFIG.scope
      })

      const { access_token, refresh_token, expires_in } = response.data

      // Store tokens
      this.storeTokens(access_token, refresh_token, expires_in)

      // Get user info
      const userInfo = await this.getUserInfo()

      return { success: true, userInfo }
    } catch (error) {
      console.error('Login failed:', error)
      return {
        success: false,
        error: error.response?.data?.error_description || 'Login failed'
      }
    }
  }

  // Authorization code flow (redirect to auth server)
  initiateAuthorizationCodeFlow() {
    const state = this.generateState()
    localStorage.setItem('oauth_state', state)

    const authUrl = buildAuthorizationUrl(state)
    window.location.href = authUrl
  }

  // Handle authorization code callback
  async handleAuthorizationCallback(code, state) {
    try {
      // Verify state parameter
      const storedState = localStorage.getItem('oauth_state')
      if (state !== storedState) {
        throw new Error('Invalid state parameter')
      }
      localStorage.removeItem('oauth_state')

      // Exchange code for tokens
      const response = await axios.post(getOAuthUrl(OAUTH_CONFIG.endpoints.token), {
        grant_type: OAUTH_CONFIG.grantTypes.authorizationCode,
        code,
        redirect_uri: OAUTH_CONFIG.redirectUri,
        client_id: OAUTH_CONFIG.clientId,
        client_secret: OAUTH_CONFIG.clientSecret
      })

      const { access_token, refresh_token, expires_in } = response.data

      // Store tokens
      this.storeTokens(access_token, refresh_token, expires_in)

      // Get user info
      const userInfo = await this.getUserInfo()

      return { success: true, userInfo }
    } catch (error) {
      console.error('Authorization callback failed:', error)
      return {
        success: false,
        error: error.response?.data?.error_description || 'Authorization failed'
      }
    }
  }

  // Refresh access token
  async refreshToken() {
    const refreshToken = localStorage.getItem(OAUTH_CONFIG.storage.refreshToken)
    if (!refreshToken) return false

    try {
      const response = await axios.post(getOAuthUrl(OAUTH_CONFIG.endpoints.token), {
        grant_type: OAUTH_CONFIG.grantTypes.refreshToken,
        refresh_token: refreshToken,
        client_id: OAUTH_CONFIG.clientId,
        client_secret: OAUTH_CONFIG.clientSecret
      })

      const { access_token, expires_in } = response.data

      // Store new access token
      this.storeTokens(access_token, refreshToken, expires_in)

      return true
    } catch (error) {
      console.error('Token refresh failed:', error)
      return false
    }
  }

  // Get user information
  async getUserInfo() {
    try {
      const response = await axios.get(getOAuthUrl(OAUTH_CONFIG.endpoints.userInfo))
      const userInfo = response.data
      localStorage.setItem(OAUTH_CONFIG.storage.userInfo, JSON.stringify(userInfo))
      return userInfo
    } catch (error) {
      console.error('Failed to get user info:', error)
      throw error
    }
  }

  // Verify JWT token using RS256
  async verifyToken(token) {
    try {
      const jwks = await this.getJWKS()
      const JWKS = jose.createLocalJWKSet(jwks)

      const { payload } = await jose.jwtVerify(token, JWKS, {
        issuer: 'oauth2-auth-server',
        algorithms: ['RS256']
      })

      return { valid: true, payload }
    } catch (error) {
      console.error('Token verification failed:', error)
      return { valid: false, error: error.message }
    }
  }

  // Decode JWT token (without verification)
  decodeToken(token) {
    try {
      return jwtDecode(token)
    } catch (error) {
      console.error('Token decode failed:', error)
      return null
    }
  }

  // Store tokens in localStorage
  storeTokens(accessToken, refreshToken, expiresIn) {
    const expiryTime = Date.now() + (expiresIn * 1000)

    localStorage.setItem(OAUTH_CONFIG.storage.accessToken, accessToken)
    localStorage.setItem(OAUTH_CONFIG.storage.refreshToken, refreshToken)
    localStorage.setItem(OAUTH_CONFIG.storage.tokenExpiry, expiryTime.toString())
  }

  // Get stored access token
  getAccessToken() {
    const token = localStorage.getItem(OAUTH_CONFIG.storage.accessToken)
    const expiry = localStorage.getItem(OAUTH_CONFIG.storage.tokenExpiry)

    if (!token || !expiry) return null

    // Check if token is expired
    if (Date.now() > parseInt(expiry)) {
      this.clearTokens()
      return null
    }

    return token
  }

  // Get stored user info
  getUserInfoFromStorage() {
    const userInfo = localStorage.getItem(OAUTH_CONFIG.storage.userInfo)
    return userInfo ? JSON.parse(userInfo) : null
  }

  // Check if user is authenticated
  isAuthenticated() {
    return !!this.getAccessToken()
  }

  // Logout user
  async logout() {
    const accessToken = this.getAccessToken()

    if (accessToken) {
      try {
        // Revoke token on server
        await axios.post(getOAuthUrl(OAUTH_CONFIG.endpoints.revoke), {
          token: accessToken,
          client_id: OAUTH_CONFIG.clientId,
          client_secret: OAUTH_CONFIG.clientSecret
        })
      } catch (error) {
        console.error('Token revocation failed:', error)
      }
    }

    this.clearTokens()
  }

  // Clear stored tokens
  clearTokens() {
    localStorage.removeItem(OAUTH_CONFIG.storage.accessToken)
    localStorage.removeItem(OAUTH_CONFIG.storage.refreshToken)
    localStorage.removeItem(OAUTH_CONFIG.storage.tokenExpiry)
    localStorage.removeItem(OAUTH_CONFIG.storage.userInfo)
  }

  // Generate random state for CSRF protection
  generateState() {
    return Math.random().toString(36).substring(2, 15) +
           Math.random().toString(36).substring(2, 15)
  }
}

export default new OAuthService()
