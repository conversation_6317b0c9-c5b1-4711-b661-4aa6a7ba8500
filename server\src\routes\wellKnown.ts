import { Router } from 'express'
import { getJ<PERSON><PERSON>, getPublic<PERSON>ey, getServerMetadata } from '../controllers/jwksController'

const router = Router()

// JWKS endpoint (RFC 7517)
router.get('/jwks.json', getJWKS)

// OAuth server metadata endpoint (RFC 8414)
router.get('/oauth-authorization-server', getServerMetadata)

// Simple public key endpoint
router.get('/public-key', getPublicKey)

export default router
