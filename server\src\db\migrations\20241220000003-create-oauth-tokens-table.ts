import { sql, type Kysely } from 'kysely'

export async function up(db: Kysely<any>): Promise<void> {
  await db.schema
    .createTable('oauth_tokens')
    .addColumn('id', 'serial', (col) => col.primary<PERSON>ey())
    .addColumn('access_token', 'varchar(500)', (col) => col.notNull().unique())
    .addColumn('refresh_token', 'varchar(500)', (col) => col)
    .addColumn('token_type', 'varchar(50)', (col) => col.notNull().defaultTo('Bearer'))
    .addColumn('expires_at', 'timestamp', (col) => col.notNull())
    .addColumn('scope', 'text', (col) => col)
    .addColumn('client_id', 'varchar(255)', (col) => col.notNull())
    .addColumn('employee_id', 'integer', (col) => col)
    .addColumn('authorization_code', 'varchar(255)', (col) => col)
    .addColumn('code_challenge', 'varchar(255)', (col) => col)
    .addColumn('code_challenge_method', 'varchar(10)', (col) => col)
    .addColumn('redirect_uri', 'text', (col) => col)
    .addColumn('is_revoked', 'boolean', (col) => col.notNull().defaultTo(false))
    .addColumn('created_at', 'timestamp', (col) =>
      col.defaultTo(sql`now()`).notNull()
    )
    .addColumn('updated_at', 'timestamp', (col) =>
      col.defaultTo(sql`now()`).notNull()
    )
    .execute()

  // Add foreign key constraints
  await db.schema
    .alterTable('oauth_tokens')
    .addForeignKeyConstraint(
      'fk_oauth_tokens_client_id',
      ['client_id'],
      'oauth_clients',
      ['client_id']
    )
    .execute()

  await db.schema
    .alterTable('oauth_tokens')
    .addForeignKeyConstraint(
      'fk_oauth_tokens_employee_id',
      ['employee_id'],
      'employees',
      ['employee_id']
    )
    .execute()

  // Create indexes for better performance
  await db.schema
    .createIndex('idx_oauth_tokens_access_token')
    .on('oauth_tokens')
    .column('access_token')
    .execute()

  await db.schema
    .createIndex('idx_oauth_tokens_refresh_token')
    .on('oauth_tokens')
    .column('refresh_token')
    .execute()

  await db.schema
    .createIndex('idx_oauth_tokens_client_id')
    .on('oauth_tokens')
    .column('client_id')
    .execute()

  await db.schema
    .createIndex('idx_oauth_tokens_employee_id')
    .on('oauth_tokens')
    .column('employee_id')
    .execute()

  await db.schema
    .createIndex('idx_oauth_tokens_authorization_code')
    .on('oauth_tokens')
    .column('authorization_code')
    .execute()

  await db.schema
    .createIndex('idx_oauth_tokens_expires_at')
    .on('oauth_tokens')
    .column('expires_at')
    .execute()

  await db.schema
    .createIndex('idx_oauth_tokens_is_revoked')
    .on('oauth_tokens')
    .column('is_revoked')
    .execute()
}

export async function down(db: Kysely<any>): Promise<void> {
  await db.schema.dropTable('oauth_tokens').execute()
}
