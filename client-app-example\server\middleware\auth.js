import jwt from 'jsonwebtoken'
import * as jose from 'jose'
import axios from 'axios'

class AuthMiddleware {
  constructor() {
    this.jwksCache = null
    this.jwksCacheExpiry = 0
    this.jwksUrl = process.env.JWKS_URL || 'http://localhost:3230/.well-known/jwks.json'
    this.oauthServerUrl = process.env.OAUTH_SERVER_URL || 'http://localhost:3230'
    this.jwtIssuer = process.env.JWT_ISSUER || 'oauth2-auth-server'
    this.cacheTTL = parseInt(process.env.JWKS_CACHE_TTL) || 3600000 // 1 hour
  }

  /**
   * Get JWKS from OAuth server with caching
   */
  async getJWKS() {
    const now = Date.now()

    // Return cached JWKS if still valid
    if (this.jwksCache && now < this.jwksCacheExpiry) {
      return this.jwksCache
    }

    try {
      console.log('Fetching JW<PERSON> from OAuth server...')
      const response = await axios.get(this.jwksUrl, { timeout: 5000 })

      this.jwksCache = response.data
      this.jwksCacheExpiry = now + this.cacheTTL

      console.log(`JWKS cached for ${this.cacheTTL / 1000} seconds`)
      return this.jwksCache
    } catch (error) {
      console.error('Failed to fetch JWKS:', error.message)

      // Return cached JWKS if available, even if expired
      if (this.jwksCache) {
        console.warn('Using expired JWKS cache due to fetch failure')
        return this.jwksCache
      }

      throw new Error('JWKS not available')
    }
  }

  /**
   * Verify JWT token using RS256 public key
   */
  async verifyToken(token) {
    try {
      const jwks = await this.getJWKS()
      const JWKS = jose.createLocalJWKSet(jwks)

      const { payload } = await jose.jwtVerify(token, JWKS, {
        issuer: this.jwtIssuer,
        algorithms: ['RS256']
      })

      return { valid: true, payload }
    } catch (error) {
      console.error('Token verification failed:', error.message)
      return { valid: false, error: error.message }
    }
  }

  /**
   * Get employee information from OAuth server
   */
  async getEmployeeInfo(token) {
    try {
      const response = await axios.get(`${this.oauthServerUrl}/api/v1/users/me`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        timeout: 5000
      })

      return { success: true, employee: response.data }
    } catch (error) {
      console.error('Failed to get employee info:', error.message)
      return {
        success: false,
        error: error.response?.data || error.message
      }
    }
  }

  /**
   * Extract JWT token from request (Authorization header or HTTP-only cookie)
   */
  extractToken(req) {
    // Try Authorization header first
    const authHeader = req.headers.authorization
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return {
        token: authHeader.substring(7),
        source: 'header'
      }
    }

    // Try HTTP-only cookie
    const cookieToken = req.cookies?.access_token
    if (cookieToken) {
      return {
        token: cookieToken,
        source: 'cookie'
      }
    }

    return null
  }

  /**
   * Express middleware for JWT authentication
   */
  authenticate() {
    return async (req, res, next) => {
      try {
        // Extract token from Authorization header or cookie
        const tokenInfo = this.extractToken(req)
        if (!tokenInfo) {
          return res.status(401).json({
            error: 'unauthorized',
            message: 'Missing or invalid Authorization header or access_token cookie'
          })
        }

        const token = tokenInfo.token

        // Verify token signature
        const verification = await this.verifyToken(token)
        if (!verification.valid) {
          return res.status(401).json({
            error: 'invalid_token',
            message: 'Token verification failed',
            details: verification.error
          })
        }

        // Add token payload to request
        req.tokenPayload = verification.payload
        req.accessToken = token

        next()
      } catch (error) {
        console.error('Authentication middleware error:', error)
        res.status(500).json({
          error: 'server_error',
          message: 'Authentication failed'
        })
      }
    }
  }

  /**
   * Express middleware for getting employee information from JWT token
   */
  getEmployee() {
    return async (req, res, next) => {
      try {
        // Ensure authentication middleware has run first
        if (!req.tokenPayload) {
          return res.status(401).json({
            error: 'unauthorized',
            message: 'Authentication required'
          })
        }

        // Get employee information directly from JWT token
        if (req.tokenPayload.employee) {
          // Employee info is embedded in the JWT token
          req.employee = {
            employee_id: req.tokenPayload.employee.employee_id,
            first_name: req.tokenPayload.employee.first_name,
            last_name: req.tokenPayload.employee.last_name,
            nickname: req.tokenPayload.employee.nickname,
            // Add computed fields for compatibility
            full_name: `${req.tokenPayload.employee.first_name} ${req.tokenPayload.employee.last_name}`,
            display_name: req.tokenPayload.employee.nickname || req.tokenPayload.employee.first_name
          }

          console.log(`Employee info extracted from JWT: ${req.employee.full_name} (ID: ${req.employee.employee_id})`)
          next()
        } else if (req.tokenPayload.employee_id) {
          // Fallback: if employee info not in JWT, fetch from OAuth server
          console.log('Employee info not in JWT, fetching from OAuth server...')
          const result = await this.getEmployeeInfo(req.accessToken)

          if (!result.success) {
            return res.status(403).json({
              error: 'forbidden',
              message: 'Failed to get employee information',
              details: result.error
            })
          }

          req.employee = result.employee
          next()
        } else {
          // No employee information available
          return res.status(403).json({
            error: 'forbidden',
            message: 'No employee information available in token'
          })
        }
      } catch (error) {
        console.error('Employee middleware error:', error)
        res.status(500).json({
          error: 'server_error',
          message: 'Failed to get employee information'
        })
      }
    }
  }

  /**
   * Express middleware for optional authentication
   */
  optionalAuth() {
    return async (req, res, next) => {
      try {
        // Extract token from Authorization header or cookie
        const tokenInfo = this.extractToken(req)

        if (!tokenInfo) {
          // No token provided, continue without authentication
          return next()
        }

        const token = tokenInfo.token
        const verification = await this.verifyToken(token)

        if (verification.valid) {
          req.tokenPayload = verification.payload
          req.accessToken = token

          // Get employee info from JWT token if available
          if (verification.payload.employee) {
            req.employee = {
              employee_id: verification.payload.employee.employee_id,
              first_name: verification.payload.employee.first_name,
              last_name: verification.payload.employee.last_name,
              nickname: verification.payload.employee.nickname,
              full_name: `${verification.payload.employee.first_name} ${verification.payload.employee.last_name}`,
              display_name: verification.payload.employee.nickname || verification.payload.employee.first_name
            }
          } else if (verification.payload.employee_id) {
            // Fallback: fetch from OAuth server if not in JWT
            const employeeResult = await this.getEmployeeInfo(token)
            if (employeeResult.success) {
              req.employee = employeeResult.employee
            }
          }
        }

        next()
      } catch (error) {
        console.error('Optional auth middleware error:', error)
        // Continue without authentication on error
        next()
      }
    }
  }
}

// Export singleton instance
export default new AuthMiddleware()
