import { db } from '../db/database'
import { OAuthToken, NewOAuthToken, OAuthTokenUpdate } from '../db/models'
import { oauthClientService } from './oauthClientService'
import { webhookService } from './webhookService'
import * as jwt from 'jsonwebtoken'
import * as crypto from 'crypto'

export interface TokenPayload {
  sub: string // subject (employee_id or client_id)
  client_id: string
  scope?: string
  employee_id?: number
  iat: number
  exp: number
}

export interface AuthorizationCodeData {
  client_id: string
  employee_id?: number
  redirect_uri: string
  scope?: string
  code_challenge?: string
  code_challenge_method?: string
}

export class OAuthTokenService {
  private readonly jwtSecret = process.env.JWT_SECRET || 'your-secret-key'
  private readonly jwtAlgorithm = (process.env.JWT_ALGORITHM as jwt.Algorithm) || 'HS256'
  private readonly accessTokenExpiry = Number(process.env.OAUTH_ACCESS_TOKEN_EXPIRY) || 3600 // 1 hour in seconds
  private readonly refreshTokenExpiry = Number(process.env.OAUTH_REFRESH_TOKEN_EXPIRY) || 86400 * 30 // 30 days in seconds
  private readonly authCodeExpiry = Number(process.env.OAUTH_AUTH_CODE_EXPIRY) || 600 // 10 minutes in seconds

  async generateAccessToken(payload: Omit<TokenPayload, 'iat' | 'exp'>): Promise<string> {
    const now = Math.floor(Date.now() / 1000)
    const tokenPayload: TokenPayload = {
      ...payload,
      iat: now,
      exp: now + this.accessTokenExpiry,
    }

    return jwt.sign(tokenPayload, this.jwtSecret, {
      algorithm: this.jwtAlgorithm,
      issuer: 'oauth2-auth-server',
      audience: payload.client_id
    })
  }

  async generateRefreshToken(): Promise<string> {
    return crypto.randomBytes(32).toString('hex')
  }

  async generateAuthorizationCode(): Promise<string> {
    return crypto.randomBytes(32).toString('hex')
  }

  async createTokens(data: {
    client_id: string
    employee_id?: number
    scope?: string
    redirect_uri?: string
  }): Promise<{ access_token: string; refresh_token: string; expires_in: number }> {
    const accessToken = await this.generateAccessToken({
      sub: data.employee_id ? data.employee_id.toString() : data.client_id,
      client_id: data.client_id,
      scope: data.scope,
      employee_id: data.employee_id,
    })

    const refreshToken = await this.generateRefreshToken()
    const expiresAt = new Date(Date.now() + this.accessTokenExpiry * 1000)

    const tokenData: NewOAuthToken = {
      access_token: accessToken,
      refresh_token: refreshToken,
      token_type: 'Bearer',
      expires_at: expiresAt.toISOString(),
      scope: data.scope,
      client_id: data.client_id,
      employee_id: data.employee_id,
      redirect_uri: data.redirect_uri,
      is_revoked: false,
    }

    await db.insertInto('oauth_tokens').values(tokenData).execute()

    return {
      access_token: accessToken,
      refresh_token: refreshToken,
      expires_in: this.accessTokenExpiry,
    }
  }

  async createAuthorizationCode(data: AuthorizationCodeData): Promise<string> {
    const authCode = await this.generateAuthorizationCode()
    const expiresAt = new Date(Date.now() + this.authCodeExpiry * 1000)

    const tokenData: NewOAuthToken = {
      access_token: '', // Will be generated when code is exchanged
      authorization_code: authCode,
      token_type: 'Bearer',
      expires_at: expiresAt.toISOString(),
      scope: data.scope,
      client_id: data.client_id,
      employee_id: data.employee_id,
      redirect_uri: data.redirect_uri,
      code_challenge: data.code_challenge,
      code_challenge_method: data.code_challenge_method,
      is_revoked: false,
    }

    await db.insertInto('oauth_tokens').values(tokenData).execute()

    return authCode
  }

  async exchangeAuthorizationCode(
    code: string,
    client_id: string,
    redirect_uri: string,
    code_verifier?: string
  ): Promise<{ access_token: string; refresh_token: string; expires_in: number } | null> {
    const tokenRecord = await db
      .selectFrom('oauth_tokens')
      .selectAll()
      .where('authorization_code', '=', code)
      .where('client_id', '=', client_id)
      .where('redirect_uri', '=', redirect_uri)
      .where('is_revoked', '=', false)
      .where('expires_at', '>', new Date())
      .executeTakeFirst()

    if (!tokenRecord) {
      return null
    }

    // Validate PKCE if present
    if (tokenRecord.code_challenge && tokenRecord.code_challenge_method) {
      if (!code_verifier) {
        return null
      }

      let challenge: string
      if (tokenRecord.code_challenge_method === 'S256') {
        challenge = crypto.createHash('sha256').update(code_verifier).digest('base64url')
      } else {
        challenge = code_verifier
      }

      if (challenge !== tokenRecord.code_challenge) {
        return null
      }
    }

    // Generate new tokens
    const accessToken = await this.generateAccessToken({
      sub: tokenRecord.employee_id ? tokenRecord.employee_id.toString() : tokenRecord.client_id,
      client_id: tokenRecord.client_id,
      scope: tokenRecord.scope || undefined,
      employee_id: tokenRecord.employee_id || undefined,
    })

    const refreshToken = await this.generateRefreshToken()
    const expiresAt = new Date(Date.now() + this.accessTokenExpiry * 1000)

    // Update the token record
    await db
      .updateTable('oauth_tokens')
      .set({
        access_token: accessToken,
        refresh_token: refreshToken,
        expires_at: expiresAt.toISOString(),
        authorization_code: null, // Clear the auth code
        updated_at: new Date().toISOString(),
      })
      .where('id', '=', tokenRecord.id)
      .execute()

    return {
      access_token: accessToken,
      refresh_token: refreshToken,
      expires_in: this.accessTokenExpiry,
    }
  }

  async refreshAccessToken(refreshToken: string): Promise<{ access_token: string; expires_in: number } | null> {
    const tokenRecord = await db
      .selectFrom('oauth_tokens')
      .selectAll()
      .where('refresh_token', '=', refreshToken)
      .where('is_revoked', '=', false)
      .executeTakeFirst()

    if (!tokenRecord) {
      return null
    }

    const newAccessToken = await this.generateAccessToken({
      sub: tokenRecord.employee_id ? tokenRecord.employee_id.toString() : tokenRecord.client_id,
      client_id: tokenRecord.client_id,
      scope: tokenRecord.scope || undefined,
      employee_id: tokenRecord.employee_id || undefined,
    })

    const expiresAt = new Date(Date.now() + this.accessTokenExpiry * 1000)

    await db
      .updateTable('oauth_tokens')
      .set({
        access_token: newAccessToken,
        expires_at: expiresAt.toISOString(),
        updated_at: new Date().toISOString(),
      })
      .where('id', '=', tokenRecord.id)
      .execute()

    return {
      access_token: newAccessToken,
      expires_in: this.accessTokenExpiry,
    }
  }

  async validateAccessToken(accessToken: string): Promise<TokenPayload | null> {
    try {
      const payload = jwt.verify(accessToken, this.jwtSecret, {
        algorithms: [this.jwtAlgorithm],
        issuer: 'oauth2-auth-server'
      }) as TokenPayload

      // Check if token is revoked in database
      const tokenRecord = await db
        .selectFrom('oauth_tokens')
        .select(['is_revoked'])
        .where('access_token', '=', accessToken)
        .executeTakeFirst()

      if (tokenRecord?.is_revoked) {
        return null
      }

      return payload
    } catch (error) {
      return null
    }
  }

  async revokeToken(token: string, reason?: string): Promise<boolean> {
    // First, get the token record to send webhook notification
    const tokenRecord = await db
      .selectFrom('oauth_tokens')
      .selectAll()
      .where((eb) =>
        eb.or([
          eb('access_token', '=', token),
          eb('refresh_token', '=', token),
        ])
      )
      .where('is_revoked', '=', false)
      .executeTakeFirst()

    if (!tokenRecord) {
      return false
    }

    // Revoke the token
    const result = await db
      .updateTable('oauth_tokens')
      .set({
        is_revoked: true,
        updated_at: new Date().toISOString(),
      })
      .where('id', '=', tokenRecord.id)
      .execute()

    const success = result.length > 0 && result[0].numUpdatedRows > 0

    if (success) {
      // Send webhook notification
      try {
        const client = await oauthClientService.getClientByClientId(tokenRecord.client_id)
        if (client) {
          // Determine if it's an access token or refresh token
          const isAccessToken = tokenRecord.access_token === token
          const isRefreshToken = tokenRecord.refresh_token === token

          if (isAccessToken) {
            await webhookService.sendTokenRevocationWebhook(client, {
              employee_id: tokenRecord.employee_id || undefined,
              token_id: tokenRecord.id.toString(),
              access_token: tokenRecord.access_token || undefined,
              reason: reason || 'manual_revocation',
            })
          }

          if (isRefreshToken) {
            await webhookService.sendRefreshTokenRevocationWebhook(client, {
              employee_id: tokenRecord.employee_id || undefined,
              token_id: tokenRecord.id.toString(),
              refresh_token: tokenRecord.refresh_token || undefined,
              reason: reason || 'manual_revocation',
            })
          }
        }
      } catch (error) {
        console.error('Failed to send webhook notification:', error)
        // Don't fail the revocation if webhook fails
      }
    }

    return success
  }

  async cleanupExpiredTokens(): Promise<number> {
    const result = await db
      .deleteFrom('oauth_tokens')
      .where('expires_at', '<', new Date())
      .where('is_revoked', '=', true)
      .execute()

    return result.length > 0 ? Number(result[0].numDeletedRows) : 0
  }
}

export const oauthTokenService = new OAuthTokenService()
