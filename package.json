{"name": "oauth2-auth-system", "version": "0.1.0", "main": "index.js", "scripts": {"build": "npm run build:server && npm run build:client", "build:server": "cd server && npm run build", "build:client": "cd client && npm run build", "dev": "npm run dev:full", "dev:full": "concurrently \"npm run dev:server\" \"npm run dev:client\"", "dev:server": "cd server && npm run dev", "dev:client": "cd client && npm run dev", "install:all": "npm install && npm run install:server && npm run install:client", "install:server": "cd server && npm install", "install:client": "cd client && npm install", "start": "cd server && npm start", "example": "concurrently \"npm run example:server\" \"npm run example:client\"", "example:server": "cd app-example/server && npm run dev", "example:client": "cd app-example/client && npm run dev", "db:inspect": "cd server && npm run db:inspect", "db:query": "cd server && npm run db:query"}, "keywords": [], "author": "", "license": "ISC", "description": "OAuth 2.0 Authentication Server with React frontend and Node.js backend", "devDependencies": {"concurrently": "^9.1.2"}, "dependencies": {}}