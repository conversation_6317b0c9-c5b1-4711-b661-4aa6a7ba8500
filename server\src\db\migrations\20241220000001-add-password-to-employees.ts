import { sql, type <PERSON><PERSON><PERSON> } from 'kysely'

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  // This migration was already partially applied
  // The password_hash and username columns already exist
  // Skip this migration as the columns are already there
  console.log('Password and username columns already exist, skipping migration')

  // Create indexes for better performance
  await db.schema
    .createIndex('idx_employees_username')
    .on('employees')
    .column('username')
    .execute()

  await db.schema
    .createIndex('idx_employees_email_auth')
    .on('employees')
    .column('email')
    .execute()

  console.log('Added password_hash, username, and OAuth-related columns to employees table')
}

export async function down(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
}
