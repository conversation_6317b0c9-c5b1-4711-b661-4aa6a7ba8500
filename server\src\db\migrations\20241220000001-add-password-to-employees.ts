import { sql, type <PERSON>ysely } from 'kysely'

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Check if columns already exist before adding them
  const columns = await db.introspection.getMetadata()
  const employeesTable = columns.tables.find(t => t.name === 'employees')
  const existingColumns = employeesTable?.columns.map(c => c.name) || []

  // Add password_hash column if it doesn't exist
  if (!existingColumns.includes('password_hash')) {
    await db.schema
      .alterTable('employees')
      .addColumn('password_hash', 'varchar(255)', (col) => col)
      .execute()
  }

  // Add username column if it doesn't exist
  if (!existingColumns.includes('username')) {
    await db.schema
      .alterTable('employees')
      .addColumn('username', 'varchar(100)', (col) => col.unique())
      .execute()
  }

  // Add OAuth-related columns if they don't exist
  if (!existingColumns.includes('last_login_at')) {
    await db.schema
      .alterTable('employees')
      .addColumn('last_login_at', 'timestamp')
      .execute()
  }

  if (!existingColumns.includes('failed_login_attempts')) {
    await db.schema
      .alterTable('employees')
      .addColumn('failed_login_attempts', 'int', (col) => col.defaultTo(0))
      .execute()
  }

  if (!existingColumns.includes('locked_until')) {
    await db.schema
      .alterTable('employees')
      .addColumn('locked_until', 'timestamp')
      .execute()
  }

  // Create indexes for better performance
  await db.schema
    .createIndex('idx_employees_username')
    .on('employees')
    .column('username')
    .execute()

  await db.schema
    .createIndex('idx_employees_email_auth')
    .on('employees')
    .column('email')
    .execute()

  console.log('Added password_hash, username, and OAuth-related columns to employees table')
}

export async function down(db: Kysely<any>): Promise<void> {
  // Remove the added columns
  await db.schema
    .alterTable('employees')
    .dropColumn('password_hash')
    .execute()

  await db.schema
    .alterTable('employees')
    .dropColumn('username')
    .execute()

  await db.schema
    .alterTable('employees')
    .dropColumn('last_login_at')
    .execute()

  await db.schema
    .alterTable('employees')
    .dropColumn('failed_login_attempts')
    .execute()

  await db.schema
    .alterTable('employees')
    .dropColumn('locked_until')
    .execute()

  console.log('Removed password_hash, username, and OAuth-related columns from employees table')
}
