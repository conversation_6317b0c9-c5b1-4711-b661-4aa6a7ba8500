import express from 'express'
import axios from 'axios'
import authMiddleware from '../middleware/auth.js'

const router = express.Router()

// OAuth server configuration
const OAUTH_SERVER_URL = process.env.OAUTH_SERVER_URL || 'http://localhost:3230'

/**
 * Public endpoint - no authentication required
 */
router.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    server: 'client-app-example-server',
    version: '1.0.0'
  })
})

/**
 * Authentication endpoint - login with credentials and set HTTP-only cookie
 */
router.post('/auth/login', async (req, res) => {
  try {
    const { username, password, client_id, client_secret } = req.body

    if (!username || !password || !client_id || !client_secret) {
      return res.status(400).json({
        error: 'bad_request',
        message: 'Missing required fields: username, password, client_id, client_secret'
      })
    }

    // Get OAuth token from authorization server
    const tokenResponse = await axios.post(`${OAUTH_SERVER_URL}/oauth/token`, {
      grant_type: 'password',
      username,
      password,
      client_id,
      client_secret,
      scope: 'read write'
    })

    const { access_token, expires_in } = tokenResponse.data

    // Set HTTP-only cookie with the access token
    const cookieOptions = {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production', // HTTPS only in production
      sameSite: 'strict',
      maxAge: expires_in * 1000, // Convert seconds to milliseconds
      path: '/'
    }

    res.cookie('access_token', access_token, cookieOptions)

    // Extract employee info from JWT for frontend
    const verification = await authMiddleware.verifyToken(access_token)

    let employeeInfo = null
    if (verification.valid && verification.payload.employee) {
      employeeInfo = {
        employee_id: verification.payload.employee.employee_id,
        first_name: verification.payload.employee.first_name,
        last_name: verification.payload.employee.last_name,
        nickname: verification.payload.employee.nickname,
        full_name: `${verification.payload.employee.first_name} ${verification.payload.employee.last_name}`,
        display_name: verification.payload.employee.nickname || verification.payload.employee.first_name
      }
    }

    res.json({
      message: 'Login successful',
      authenticated: true,
      employee: employeeInfo,
      expires_in,
      token_source: 'http_only_cookie'
    })

  } catch (error) {
    console.error('Login error:', error.message)

    if (error.response?.status === 401) {
      return res.status(401).json({
        error: 'unauthorized',
        message: 'Invalid credentials'
      })
    }

    res.status(500).json({
      error: 'server_error',
      message: 'Login failed'
    })
  }
})

/**
 * Logout endpoint - clear HTTP-only cookie
 */
router.post('/auth/logout', (req, res) => {
  res.clearCookie('access_token', {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    path: '/'
  })

  res.json({
    message: 'Logout successful',
    authenticated: false
  })
})

/**
 * Get current user info from JWT cookie
 */
router.get('/auth/me', authMiddleware.optionalAuth(), (req, res) => {
  if (!req.tokenPayload) {
    return res.json({
      authenticated: false,
      employee: null
    })
  }

  let employeeInfo = null
  if (req.employee) {
    employeeInfo = {
      employee_id: req.employee.employee_id,
      first_name: req.employee.first_name,
      last_name: req.employee.last_name,
      nickname: req.employee.nickname,
      full_name: req.employee.full_name,
      display_name: req.employee.display_name
    }
  }

  res.json({
    authenticated: true,
    employee: employeeInfo,
    token_expires_at: new Date(req.tokenPayload.exp * 1000).toISOString()
  })
})

/**
 * Public endpoint with optional authentication
 */
router.get('/info', authMiddleware.optionalAuth(), (req, res) => {
  const response = {
    message: 'Client App Example Server',
    timestamp: new Date().toISOString(),
    authenticated: !!req.tokenPayload,
    features: [
      'JWT RS256 Token Verification',
      'Employee Information Middleware',
      'OAuth Server Integration',
      'JWKS Public Key Discovery'
    ]
  }

  if (req.tokenPayload) {
    response.user = {
      employee_id: req.tokenPayload.employee_id,
      client_id: req.tokenPayload.client_id,
      scope: req.tokenPayload.scope,
      expires_at: new Date(req.tokenPayload.exp * 1000).toISOString()
    }
  }

  if (req.employee) {
    response.employee = {
      employee_id: req.employee.employee_id,
      first_name: req.employee.first_name,
      last_name: req.employee.last_name,
      nickname: req.employee.nickname,
      full_name: req.employee.full_name,
      display_name: req.employee.display_name,
      source: req.tokenPayload?.employee ? 'jwt_token' : 'oauth_server'
    }
  }

  res.json(response)
})

/**
 * Protected endpoint - requires valid JWT token
 */
router.get('/profile', authMiddleware.authenticate(), authMiddleware.getEmployee(), (req, res) => {
  res.json({
    message: 'Employee profile retrieved successfully',
    timestamp: new Date().toISOString(),
    token_info: {
      employee_id: req.tokenPayload.employee_id,
      client_id: req.tokenPayload.client_id,
      scope: req.tokenPayload.scope,
      issued_at: new Date(req.tokenPayload.iat * 1000).toISOString(),
      expires_at: new Date(req.tokenPayload.exp * 1000).toISOString(),
      issuer: req.tokenPayload.iss,
      audience: req.tokenPayload.aud
    },
    employee: req.employee
  })
})

/**
 * Protected endpoint - employee dashboard data
 */
router.get('/dashboard', authMiddleware.authenticate(), authMiddleware.getEmployee(), (req, res) => {
  const employee = req.employee

  res.json({
    message: 'Dashboard data retrieved successfully',
    timestamp: new Date().toISOString(),
    dashboard: {
      welcome_message: `Welcome back, ${employee.first_name}!`,
      employee_info: {
        employee_id: employee.employee_id,
        first_name: employee.first_name,
        last_name: employee.last_name,
        nickname: employee.nickname,
        full_name: employee.full_name,
        display_name: employee.display_name,
        data_source: req.tokenPayload?.employee ? 'jwt_token' : 'oauth_server_api'
      },
      permissions: {
        scope: req.tokenPayload.scope,
        client_id: req.tokenPayload.client_id,
        can_read: req.tokenPayload.scope.includes('read'),
        can_write: req.tokenPayload.scope.includes('write')
      },
      server_info: {
        server_time: new Date().toISOString(),
        token_expires_in: Math.max(0, req.tokenPayload.exp - Math.floor(Date.now() / 1000)),
        jwt_algorithm: 'RS256'
      }
    }
  })
})

/**
 * Protected endpoint - employee tasks/activities
 */
router.get('/tasks', authMiddleware.authenticate(), authMiddleware.getEmployee(), (req, res) => {
  const employee = req.employee

  // Mock task data based on employee
  const tasks = [
    {
      id: 1,
      title: 'Review OAuth Integration',
      description: 'Review the new OAuth 2.0 implementation with RS256 tokens',
      status: 'in_progress',
      priority: 'high',
      assigned_to: employee.employee_id,
      due_date: new Date(Date.now() + 86400000 * 3).toISOString() // 3 days from now
    },
    {
      id: 2,
      title: 'Update Security Documentation',
      description: 'Document the new JWT verification process',
      status: 'pending',
      priority: 'medium',
      assigned_to: employee.employee_id,
      due_date: new Date(Date.now() + 86400000 * 7).toISOString() // 7 days from now
    },
    {
      id: 3,
      title: 'Test Client Applications',
      description: 'Verify OAuth client integration works correctly',
      status: 'completed',
      priority: 'high',
      assigned_to: employee.employee_id,
      completed_at: new Date(Date.now() - 86400000).toISOString() // 1 day ago
    }
  ]

  res.json({
    message: 'Tasks retrieved successfully',
    timestamp: new Date().toISOString(),
    employee: {
      employee_id: employee.employee_id,
      first_name: employee.first_name,
      last_name: employee.last_name,
      full_name: employee.full_name,
      display_name: employee.display_name,
      data_source: req.tokenPayload?.employee ? 'jwt_token' : 'oauth_server_api'
    },
    tasks: tasks,
    summary: {
      total: tasks.length,
      completed: tasks.filter(t => t.status === 'completed').length,
      in_progress: tasks.filter(t => t.status === 'in_progress').length,
      pending: tasks.filter(t => t.status === 'pending').length
    }
  })
})

/**
 * Protected endpoint - test token verification
 */
router.get('/verify-token', authMiddleware.authenticate(), (req, res) => {
  res.json({
    message: 'Token verification successful',
    timestamp: new Date().toISOString(),
    token_valid: true,
    payload: req.tokenPayload,
    verification_method: 'RS256 with JWKS public key',
    server: 'client-app-example-server'
  })
})

export default router
