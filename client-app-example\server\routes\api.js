import express from 'express'
import authMiddleware from '../middleware/auth.js'

const router = express.Router()

/**
 * Public endpoint - no authentication required
 */
router.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    server: 'client-app-example-server',
    version: '1.0.0'
  })
})

/**
 * Public endpoint with optional authentication
 */
router.get('/info', authMiddleware.optionalAuth(), (req, res) => {
  const response = {
    message: 'Client App Example Server',
    timestamp: new Date().toISOString(),
    authenticated: !!req.tokenPayload,
    features: [
      'JWT RS256 Token Verification',
      'Employee Information Middleware',
      'OAuth Server Integration',
      'JWKS Public Key Discovery'
    ]
  }

  if (req.tokenPayload) {
    response.user = {
      employee_id: req.tokenPayload.employee_id,
      client_id: req.tokenPayload.client_id,
      scope: req.tokenPayload.scope,
      expires_at: new Date(req.tokenPayload.exp * 1000).toISOString()
    }
  }

  if (req.employee) {
    response.employee = {
      name: `${req.employee.first_name} ${req.employee.last_name}`,
      employee_id: req.employee.employee_id,
      department: req.employee.department,
      position: req.employee.position
    }
  }

  res.json(response)
})

/**
 * Protected endpoint - requires valid JWT token
 */
router.get('/profile', authMiddleware.authenticate(), authMiddleware.getEmployee(), (req, res) => {
  res.json({
    message: 'Employee profile retrieved successfully',
    timestamp: new Date().toISOString(),
    token_info: {
      employee_id: req.tokenPayload.employee_id,
      client_id: req.tokenPayload.client_id,
      scope: req.tokenPayload.scope,
      issued_at: new Date(req.tokenPayload.iat * 1000).toISOString(),
      expires_at: new Date(req.tokenPayload.exp * 1000).toISOString(),
      issuer: req.tokenPayload.iss,
      audience: req.tokenPayload.aud
    },
    employee: req.employee
  })
})

/**
 * Protected endpoint - employee dashboard data
 */
router.get('/dashboard', authMiddleware.authenticate(), authMiddleware.getEmployee(), (req, res) => {
  const employee = req.employee
  
  res.json({
    message: 'Dashboard data retrieved successfully',
    timestamp: new Date().toISOString(),
    dashboard: {
      welcome_message: `Welcome back, ${employee.first_name}!`,
      employee_info: {
        id: employee.employee_id,
        name: `${employee.first_name} ${employee.last_name}`,
        department: employee.department || 'Not specified',
        position: employee.position || 'Not specified',
        status: employee.is_active ? 'Active' : 'Inactive',
        last_login: employee.last_login_at || 'Never'
      },
      permissions: {
        scope: req.tokenPayload.scope,
        client_id: req.tokenPayload.client_id,
        can_read: req.tokenPayload.scope.includes('read'),
        can_write: req.tokenPayload.scope.includes('write')
      },
      server_info: {
        server_time: new Date().toISOString(),
        token_expires_in: Math.max(0, req.tokenPayload.exp - Math.floor(Date.now() / 1000)),
        jwt_algorithm: 'RS256'
      }
    }
  })
})

/**
 * Protected endpoint - employee tasks/activities
 */
router.get('/tasks', authMiddleware.authenticate(), authMiddleware.getEmployee(), (req, res) => {
  const employee = req.employee
  
  // Mock task data based on employee
  const tasks = [
    {
      id: 1,
      title: 'Review OAuth Integration',
      description: 'Review the new OAuth 2.0 implementation with RS256 tokens',
      status: 'in_progress',
      priority: 'high',
      assigned_to: employee.employee_id,
      due_date: new Date(Date.now() + 86400000 * 3).toISOString() // 3 days from now
    },
    {
      id: 2,
      title: 'Update Security Documentation',
      description: 'Document the new JWT verification process',
      status: 'pending',
      priority: 'medium',
      assigned_to: employee.employee_id,
      due_date: new Date(Date.now() + 86400000 * 7).toISOString() // 7 days from now
    },
    {
      id: 3,
      title: 'Test Client Applications',
      description: 'Verify OAuth client integration works correctly',
      status: 'completed',
      priority: 'high',
      assigned_to: employee.employee_id,
      completed_at: new Date(Date.now() - 86400000).toISOString() // 1 day ago
    }
  ]

  res.json({
    message: 'Tasks retrieved successfully',
    timestamp: new Date().toISOString(),
    employee: {
      id: employee.employee_id,
      name: `${employee.first_name} ${employee.last_name}`
    },
    tasks: tasks,
    summary: {
      total: tasks.length,
      completed: tasks.filter(t => t.status === 'completed').length,
      in_progress: tasks.filter(t => t.status === 'in_progress').length,
      pending: tasks.filter(t => t.status === 'pending').length
    }
  })
})

/**
 * Protected endpoint - test token verification
 */
router.get('/verify-token', authMiddleware.authenticate(), (req, res) => {
  res.json({
    message: 'Token verification successful',
    timestamp: new Date().toISOString(),
    token_valid: true,
    payload: req.tokenPayload,
    verification_method: 'RS256 with JWKS public key',
    server: 'client-app-example-server'
  })
})

export default router
