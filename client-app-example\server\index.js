import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import cookieParser from 'cookie-parser'
import dotenv from 'dotenv'
import apiRoutes from './routes/api.js'
import authMiddleware from './middleware/auth.js'

// Load environment variables
dotenv.config()

const app = express()
const PORT = process.env.PORT || 3001
const CORS_ORIGIN = process.env.CORS_ORIGIN || 'http://localhost:3000'

// Security middleware
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" }
}))

// CORS configuration
app.use(cors({
  origin: [CORS_ORIGIN, 'http://localhost:3000', 'http://127.0.0.1:3000'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}))

// Body parsing middleware
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true, limit: '10mb' }))

// Cookie parsing middleware
app.use(cookieParser())

// Static file serving
app.use('/demo', express.static('public'))

// Request logging middleware
app.use((req, res, next) => {
  const timestamp = new Date().toISOString()
  const method = req.method
  const url = req.url
  const userAgent = req.get('User-Agent') || 'Unknown'

  console.log(`[${timestamp}] ${method} ${url} - ${userAgent}`)

  // Log authentication header (without token value)
  if (req.headers.authorization) {
    console.log(`[${timestamp}] Auth header present: ${req.headers.authorization.substring(0, 20)}...`)
  }

  next()
})

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'Client App Example Server',
    description: 'Simple Express server demonstrating OAuth 2.0 JWT verification with employee middleware',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
    endpoints: {
      health: '/api/health',
      info: '/api/info',
      profile: '/api/profile (protected)',
      dashboard: '/api/dashboard (protected)',
      tasks: '/api/tasks (protected)',
      verify_token: '/api/verify-token (protected)'
    },
    features: [
      'JWT RS256 Token Verification',
      'Employee Information Middleware',
      'OAuth Server Integration',
      'JWKS Public Key Discovery',
      'Automatic Token Validation',
      'Employee Data Fetching'
    ],
    oauth_server: process.env.OAUTH_SERVER_URL || 'http://localhost:3230',
    cors_origin: CORS_ORIGIN
  })
})

// API routes
app.use('/api', apiRoutes)

// Test endpoint for middleware functionality
app.get('/test-auth', authMiddleware.authenticate(), authMiddleware.getEmployee(), (req, res) => {
  res.json({
    message: 'Authentication and employee middleware test successful',
    timestamp: new Date().toISOString(),
    token_payload: req.tokenPayload,
    employee_info: req.employee,
    middleware_chain: [
      'authenticate() - JWT token verification',
      'getEmployee() - Employee information fetching'
    ]
  })
})

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Server error:', err)

  res.status(err.status || 500).json({
    error: 'server_error',
    message: err.message || 'Internal server error',
    timestamp: new Date().toISOString()
  })
})

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'not_found',
    message: `Endpoint ${req.method} ${req.originalUrl} not found`,
    timestamp: new Date().toISOString(),
    available_endpoints: [
      'GET /',
      'GET /api/health',
      'GET /api/info',
      'GET /api/profile',
      'GET /api/dashboard',
      'GET /api/tasks',
      'GET /api/verify-token',
      'GET /test-auth'
    ]
  })
})

// Graceful shutdown handling
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully...')
  server.close(() => {
    console.log('Server closed')
    process.exit(0)
  })
})

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully...')
  server.close(() => {
    console.log('Server closed')
    process.exit(0)
  })
})

// Start server
const server = app.listen(PORT, () => {
  console.log('\n🚀 Client App Example Server Started!')
  console.log(`📍 Server running on: http://localhost:${PORT}`)
  console.log(`🔗 OAuth Server: ${process.env.OAUTH_SERVER_URL || 'http://localhost:3230'}`)
  console.log(`🌐 CORS Origin: ${CORS_ORIGIN}`)
  console.log(`🔐 JWT Algorithm: ${process.env.JWT_ALGORITHM || 'RS256'}`)
  console.log(`📋 Available endpoints:`)
  console.log(`   GET  /                    - Server info`)
  console.log(`   GET  /api/health          - Health check`)
  console.log(`   GET  /api/info            - Server info (optional auth)`)
  console.log(`   GET  /api/profile         - Employee profile (protected)`)
  console.log(`   GET  /api/dashboard       - Dashboard data (protected)`)
  console.log(`   GET  /api/tasks           - Employee tasks (protected)`)
  console.log(`   GET  /api/verify-token    - Token verification (protected)`)
  console.log(`   GET  /test-auth           - Middleware test (protected)`)
  console.log('\n✅ Ready to handle OAuth 2.0 JWT requests!')
})

export default app
