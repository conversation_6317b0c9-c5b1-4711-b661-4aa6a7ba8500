import { Insertable, Selectable, Updateable } from 'kysely'
import { OAuthClientsTable } from './oauth_clients'
import { OAuthTokensTable } from './oauth_tokens'
import { UsersTable } from './users'

export interface Database {
  oauth_clients: OAuthClientsTable
  oauth_tokens: OAuthTokensTable
}

// You should not use the table schema interfaces directly. Instead, you should
// use the `Selectable`, `Insertable` and `Updateable` wrappers. These wrappers
// make sure that the correct types are used in each operation.

// Export types for OAuth Clients
export type OAuthClient = Selectable<OAuthClientsTable>
export type NewOAuthClient = Insertable<OAuthClientsTable>
export type OAuthClientUpdate = Updateable<OAuthClientsTable>

// Export types for OAuth Tokens
export type OAuthToken = Selectable<OAuthTokensTable>
export type NewOAuthToken = Insertable<OAuthTokensTable>
export type OAuthTokenUpdate = Updateable<OAuthTokensTable>

// Export types for Users
export type User = Selectable<UsersTable>
export type NewUser = Insertable<UsersTable>
export type UserUpdate = Updateable<UsersTable>

// Re-export specific types from individual model files
export * from './oauth_clients'
export * from './oauth_tokens'
export * from './users'
//
// Most of the time you should trust the type inference and not use explicit
// types at all. These types can be useful when typing function arguments.

export type ApiRequest = Selectable<ApiRequestTable>
export type NewApiRequest = Insertable<ApiRequestTable>
export type ApiRequestUpdate = Updateable<ApiRequestTable>

// Work Repo Types
export type WorkRepo = Selectable<WorkRepoTable>
export type NewWorkRepo = Insertable<WorkRepoTable>
export type WorkRepoUpdate = Updateable<WorkRepoTable>

export type WorkRepoMaterial = Selectable<WorkRepoMaterialTable>
export type NewWorkRepoMaterial = Insertable<WorkRepoMaterialTable>
export type WorkRepoMaterialUpdate = Updateable<WorkRepoMaterialTable>

export type WorkRepoPiece = Selectable<WorkRepoPieceTable>
export type NewWorkRepoPiece = Insertable<WorkRepoPieceTable>
export type WorkRepoPieceUpdate = Updateable<WorkRepoPieceTable>

export type WorkRepoStatus = Selectable<WorkRepoStatusTable>
export type NewWorkRepoStatus = Insertable<WorkRepoStatusTable>
export type WorkRepoStatusUpdate = Updateable<WorkRepoStatusTable>

export type WorkRepoNote = Selectable<WorkRepoNoteTable>
export type NewWorkRepoNote = Insertable<WorkRepoNoteTable>
export type WorkRepoNoteUpdate = Updateable<WorkRepoNoteTable>

// MO Numbers Types
export type MoNumber = Selectable<MoNumbersTable>
export type NewMoNumber = Insertable<MoNumbersTable>
export type MoNumberUpdate = Updateable<MoNumbersTable>

// MO Scans Types
export type MoScan = Selectable<MoScansTable>
export type NewMoScan = Insertable<MoScansTable>
export type MoScanUpdate = Updateable<MoScansTable>

// Work Areas Types
export type WorkArea = Selectable<WorkAreasTable>
export type NewWorkArea = Insertable<WorkAreasTable>
export type WorkAreaUpdate = Updateable<WorkAreasTable>
