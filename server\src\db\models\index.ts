import { Insertable, Selectable, Updateable } from 'kysely'
import { OAuthClientsTable } from './oauth_clients'
import { OAuthTokensTable } from './oauth_tokens'
import { EmployeesTable } from './employees'

export interface Database {
  oauth_clients: OAuthClientsTable
  oauth_tokens: OAuthTokensTable
  employees: EmployeesTable
}

// You should not use the table schema interfaces directly. Instead, you should
// use the `Selectable`, `Insertable` and `Updateable` wrappers. These wrappers
// make sure that the correct types are used in each operation.

// Export types for OAuth Clients
export type OAuthClient = Selectable<OAuthClientsTable>
export type NewOAuthClient = Insertable<OAuthClientsTable>
export type OAuthClientUpdate = Updateable<OAuthClientsTable>

// Export types for OAuth Tokens
export type OAuthToken = Selectable<OAuthTokensTable>
export type NewOAuthToken = Insertable<OAuthTokensTable>
export type OAuthTokenUpdate = Updateable<OAuthTokensTable>

// Export types for Employees
export type Employee = Selectable<EmployeesTable>
export type NewEmployee = Insertable<EmployeesTable>
export type EmployeeUpdate = Updateable<EmployeesTable>

// Re-export specific types from individual model files
export * from './oauth_clients'
export * from './oauth_tokens'
export * from './employees'
