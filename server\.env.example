# Database Configuration (PostgreSQL)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=oauth2_auth_db
DB_USER=postgres
DB_PASS=your_password_here

# Server Configuration
PORT=3000
NODE_ENV=development

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# CORS Configuration
CORS_ORIGINS=http://localhost:5173,http://localhost:3000

# OAuth Configuration
OAUTH_ACCESS_TOKEN_EXPIRY=3600
OAUTH_REFRESH_TOKEN_EXPIRY=2592000
OAUTH_AUTH_CODE_EXPIRY=600
