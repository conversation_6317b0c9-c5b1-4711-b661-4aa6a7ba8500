# Database Configuration (PostgreSQL)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=oauth2_auth_db
DB_USER=postgres
DB_PASS=your_password_here

# Server Configuration
PORT=3000
NODE_ENV=development

# JWT Configuration (HMAC-SHA256)
JWT_SECRET=a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1v2w3x4y5z6a7b8c9d0e1f2
JWT_ALGORITHM=HS256

# CORS Configuration
CORS_ORIGINS=http://localhost:5173,http://localhost:3000

# OAuth Configuration
OAUTH_ACCESS_TOKEN_EXPIRY=3600
OAUTH_REFRESH_TOKEN_EXPIRY=2592000
OAUTH_AUTH_CODE_EXPIRY=600
