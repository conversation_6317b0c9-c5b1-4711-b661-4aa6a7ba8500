const fetch = require('node-fetch');
const jwt = require('jsonwebtoken');

async function testRS256Implementation() {
  try {
    console.log('🔐 Testing RS256 JWT Implementation...\n');

    // Test 1: Get server metadata
    console.log('1. Getting OAuth server metadata...');
    const metadataResponse = await fetch('http://localhost:3230/.well-known/oauth-authorization-server');

    if (!metadataResponse.ok) {
      throw new Error(`Failed to get metadata: ${await metadataResponse.text()}`);
    }

    const metadata = await metadataResponse.json();
    console.log('✅ Server metadata retrieved');
    console.log(`   Issuer: ${metadata.issuer}`);
    console.log(`   Algorithm: ${metadata.id_token_signing_alg_values_supported[0]}`);
    console.log(`   JWKS URI: ${metadata.jwks_uri}`);

    // Test 2: Get JWKS (JSON Web Key Set)
    console.log('\n2. Getting JWKS for token verification...');
    const jwksResponse = await fetch('http://localhost:3230/.well-known/jwks.json');

    if (!jwksResponse.ok) {
      throw new Error(`Failed to get JWKS: ${await jwksResponse.text()}`);
    }

    const jwks = await jwksResponse.json();
    console.log('✅ JWKS retrieved');
    console.log(`   Number of keys: ${jwks.keys.length}`);
    console.log(`   Key algorithm: ${jwks.keys[0].alg}`);
    console.log(`   Key use: ${jwks.keys[0].use}`);
    console.log(`   Key ID: ${jwks.keys[0].kid}`);

    // Test 3: Get public key (PEM format)
    console.log('\n3. Getting public key in PEM format...');
    const publicKeyResponse = await fetch('http://localhost:3230/.well-known/public-key');

    if (!publicKeyResponse.ok) {
      throw new Error(`Failed to get public key: ${await publicKeyResponse.text()}`);
    }

    const publicKeyPem = await publicKeyResponse.text();
    console.log('✅ Public key retrieved');
    console.log(`   Format: PEM`);
    console.log(`   Length: ${publicKeyPem.length} characters`);

    // Test 4: Get access token using password flow
    console.log('\n4. Getting access token with RS256 signing...');
    const tokenResponse = await fetch('http://localhost:3230/oauth/token', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        grant_type: 'password',
        username: 'william de jesus.rivera marroquin',
        password: 'admin123',
        client_id: '07b2f434-ec60-4cac-aa53-f17008ab5e6d',
        client_secret: '6eac4814cddc514c84cffaf691aae3b3d7dd83b12c56a3ea93f3aa226c0576fa'
      })
    });

    if (!tokenResponse.ok) {
      throw new Error(`Failed to get token: ${await tokenResponse.text()}`);
    }

    const tokens = await tokenResponse.json();
    console.log('✅ Access token obtained');
    console.log(`   Token type: ${tokens.token_type}`);
    console.log(`   Expires in: ${tokens.expires_in} seconds`);

    // Test 5: Decode JWT header to verify RS256
    console.log('\n5. Verifying JWT uses RS256 algorithm...');
    const tokenParts = tokens.access_token.split('.');
    const header = JSON.parse(Buffer.from(tokenParts[0], 'base64').toString());
    console.log('✅ JWT header decoded');
    console.log(`   Algorithm: ${header.alg}`);
    console.log(`   Type: ${header.typ}`);

    if (header.alg !== 'RS256') {
      throw new Error(`Expected RS256 algorithm, got ${header.alg}`);
    }

    // Test 6: Verify token signature using public key
    console.log('\n6. Verifying token signature with public key...');
    try {
      const decoded = jwt.verify(tokens.access_token, publicKeyPem, {
        algorithms: ['RS256'],
        issuer: 'oauth2-auth-server'
      });
      console.log('✅ Token signature verified successfully');
      console.log(`   Subject: ${decoded.sub}`);
      console.log(`   Employee ID: ${decoded.employee_id}`);
      console.log(`   Client ID: ${decoded.client_id}`);
      console.log(`   Issued at: ${new Date(decoded.iat * 1000).toISOString()}`);
      console.log(`   Expires at: ${new Date(decoded.exp * 1000).toISOString()}`);
    } catch (verifyError) {
      throw new Error(`Token verification failed: ${verifyError.message}`);
    }

    // Test 7: Use token to access protected resource
    console.log('\n7. Testing protected resource access...');
    const userResponse = await fetch('http://localhost:3230/api/v1/users/me', {
      headers: {
        'Authorization': `Bearer ${tokens.access_token}`,
        'Content-Type': 'application/json'
      }
    });

    if (!userResponse.ok) {
      throw new Error(`Failed to access protected resource: ${await userResponse.text()}`);
    }

    const user = await userResponse.json();
    console.log('✅ Protected resource accessed successfully');
    console.log(`   Employee: ${user.first_name} ${user.last_name}`);
    console.log(`   Email: ${user.email}`);

    console.log('\n🎉 All RS256 tests passed successfully!');
    console.log('\n📋 Summary:');
    console.log('   ✅ Server metadata endpoint working');
    console.log('   ✅ JWKS endpoint providing public key');
    console.log('   ✅ Public key endpoint working');
    console.log('   ✅ JWT tokens signed with RS256');
    console.log('   ✅ Token signature verification working');
    console.log('   ✅ Protected resources accessible with RS256 tokens');
    console.log('   ✅ OAuth flows working with RSA key pairs');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

testRS256Implementation();
