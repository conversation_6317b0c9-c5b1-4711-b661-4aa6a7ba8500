import { sql, type Kysely } from 'kysely'

export async function up(db: Kysely<any>): Promise<void> {
  await db.schema
    .createTable('oauth_clients')
    .addColumn('id', 'serial', (col) => col.primaryKey())
    .addColumn('client_id', 'varchar(255)', (col) => col.notNull().unique())
    .addColumn('client_secret', 'varchar(255)', (col) => col.notNull())
    .addColumn('client_name', 'varchar(255)', (col) => col.notNull())
    .addColumn('redirect_uris', 'jsonb', (col) => col.notNull())
    .addColumn('grant_types', 'jsonb', (col) => col.notNull())
    .addColumn('scope', 'text', (col) => col.notNull())
    .addColumn('is_active', 'boolean', (col) => col.notNull().defaultTo(true))
    .addColumn('created_at', 'timestamp', (col) =>
      col.defaultTo(sql`now()`).notNull()
    )
    .addColumn('updated_at', 'timestamp', (col) =>
      col.defaultTo(sql`now()`).notNull()
    )
    .execute()

  // Create indexes for better performance
  await db.schema
    .createIndex('idx_oauth_clients_client_id')
    .on('oauth_clients')
    .column('client_id')
    .execute()

  await db.schema
    .createIndex('idx_oauth_clients_is_active')
    .on('oauth_clients')
    .column('is_active')
    .execute()
}

export async function down(db: Kysely<any>): Promise<void> {
  await db.schema.dropTable('oauth_clients').execute()
}
