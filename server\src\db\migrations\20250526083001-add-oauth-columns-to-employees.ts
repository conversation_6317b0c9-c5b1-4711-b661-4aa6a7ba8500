import { sql, type <PERSON><PERSON><PERSON> } from 'kysely'

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  // Add OAuth-related columns to employees table
  // db.schema
  //   .alterTable('employees')
  //   .addColumn('last_login_at', 'timestamp', (col) => col.Nullable())
  //   .addColumn('failed_login_attempts', 'integer', (col) => col.defaultTo(0))
  //   .addColumn('locked_until', 'timestamp')
  //   .execute()
  await sql`
    ALTER TABLE employees 
    ADD COLUMN last_login_at TIMESTAMP NULL,
    ADD COLUMN failed_login_attempts INT DEFAULT 0,
    ADD COLUMN locked_until TIMESTAMP NULL
  `.execute(db)

  // Create indexes for better performance
  await db.schema
    .createIndex('idx_employees_username')
    .on('employees')
    .column('username')
    .ifNotExists()
    .execute()

  await db.schema
    .createIndex('idx_employees_email_auth')
    .on('employees')
    .column('email')
    .ifNotExists()
    .execute()

  console.log('Added OAuth-related columns to employees table')
}

export async function down(db: Kysely<any>): Promise<void> {
  // Remove the added columns
  db.schema
    .alterTable('employees')
    .dropColumn('last_login_at')
    .dropColumn('failed_login_attempts')
    .dropColumn('locked_until')
    .execute()
  // await sql`
  //   ALTER TABLE employees 
  //   DROP COLUMN last_login_at,
  //   DROP COLUMN failed_login_attempts,
  //   DROP COLUMN locked_until
  // `.execute(db)

  console.log('Removed OAuth-related columns from employees table')
}
